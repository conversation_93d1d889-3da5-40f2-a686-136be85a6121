---
name: Issue report
about: Create a report to help us to improve the project
labels: 'Type: Bug'

---

<!-- 
1. Please search to see if an issue already exists for the bug you encountered.
2. For support requests, FAQs or "How to" questions, please use the GitHub Discussions section instead - https://github.com/projectdiscovery/katana/discussions or
3. Join our discord server at https://discord.gg/projectdiscovery and post the question on the #katana channel.
-->

<!-- ISSUES MISSING IMPORTANT INFORMATION MAY BE CLOSED WITHOUT INVESTIGATION. -->

### katana version:
<!-- You can find current version of katana with "katana -version" -->
<!-- We only accept issues that are reproducible on the latest version of katana. -->
<!-- You can find the latest version of project at https://github.com/projectdiscovery/katana/releases/ -->

### Current Behavior:
<!-- A concise description of what you're experiencing. -->

### Expected Behavior:
<!-- A concise description of what you expected to happen. -->

### Steps To Reproduce:
<!--
Example: steps to reproduce the behavior:
1. Run 'katana ..'
2. See error...
-->


### Anything else:
<!-- Links? References? Screnshots? Anything that will give us more context about the issue that you are encountering! -->