version: 2

env:
  - GO111MODULE=on
before:
  hooks:
    - go mod tidy
project_name: katana
builds:
  - id: katana-linux-amd64-generic
    ldflags:
      - -s -w
    binary: katana
    env:
      - CGO_ENABLED=1
    main: ./cmd/katana/main.go
    goos:
      - linux
    goarch:
      - amd64

  - id: katana-linux-i386-generic
    ldflags:
      - -s -w
    binary: katana
    main: ./cmd/katana/main.go
    goos:
      - linux
    goarch:
      - 386

  - id: katana-linux-arm
    ldflags:
      - -s -w
    binary: katana
    env:
      - CGO_ENABLED=1
      - CC=aarch64-linux-gnu-gcc
    main: ./cmd/katana/main.go
    goos:
      - linux
    goarch:
      - arm64
      
archives:
- formats: zip

checksum:
  name_template: "{{ .ProjectName }}-linux-checksums.txt"

announce:
  slack:
    enabled: true
    channel: '#release'
    username: GoReleaser
    message_template: 'New Release: {{ .ProjectName }} {{.Tag}} is published! Check it out at {{ .ReleaseURL }}'

  discord:
    enabled: true
    message_template: '**New Release: {{ .ProjectName }} {{.Tag}}** is published! Check it out at {{ .ReleaseURL }}'