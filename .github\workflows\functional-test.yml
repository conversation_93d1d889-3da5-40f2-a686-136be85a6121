name: 🧪 Functional Test

on:
  pull_request:
    paths:
      - '**.go'
      - '**.mod'
  workflow_dispatch:

jobs:  
  functional:
    name: Functional Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
    steps:
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x

      - name: Check out code
        uses: actions/checkout@v4

      - name: Functional Tests
        run: |
          chmod +x run.sh
          bash run.sh ${{ matrix.os }}
        working-directory: cmd/functional-test
