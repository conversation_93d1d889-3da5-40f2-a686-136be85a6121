name: 🙏🏻 Security Crawl Maze Score

on:
  workflow_dispatch:

jobs:
  build:
    name: Run Scoring
    runs-on: ubuntu-latest-16-cores
    steps:
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x

      - name: Check out code
        uses: actions/checkout@v4

      - name: Build
        run: go build .
        working-directory: cmd/katana/

      - name: Run Katana Standard
        run: ./katana -u https://security-crawl-maze.app/ -kf all -jc -jsluice -d 10 -o output_standard.txt -cos node_modules
        working-directory: cmd/katana
        
      - name: Run Katana Headless
        run: ./katana -u https://security-crawl-maze.app/ -kf all -jc -jsluice -d 10 -headless -o output_headless.txt -cos node_modules
        working-directory: cmd/katana

      - name: Run Score
        run: go build .; ./crawl-maze-score ../../katana/output_standard.txt ../../katana/output_headless.txt
        working-directory: cmd/tools/crawl-maze-score