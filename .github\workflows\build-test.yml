name: 🔨 Build Test

on:
  workflow_dispatch:
  pull_request:
    branches:
      - dev
    paths:
      - '**.go'
      - '**.mod'
jobs:
  lint:
    name: "<PERSON><PERSON>"
    if: "${{ !endsWith(github.actor, '[bot]') }}"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: projectdiscovery/actions/setup/go@v1
      - uses: projectdiscovery/actions/golangci-lint@v1

  build:
    name: Test Builds
    needs: [lint]
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
        go-version: [1.21.x]
    steps:
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ matrix.go-version }}

      - name: Check out code
        uses: actions/checkout@v4

      - name: Test
        run: go test ./...
        working-directory: .

      - name: Build
        run: go build .
        working-directory: cmd/katana/

      - name: Integration Tests
        env:
          GH_ACTION: true
        run: bash run.sh
        working-directory: integration_tests/

      - name: Install
        run: go install
        working-directory: cmd/katana/

      - name: Race Condition Tests
        run: go build -race .
        working-directory: cmd/katana/

      
